<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>中奖记录页面预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .records-page {
            max-width: 375px;
            margin: 0 auto;
            background: transparent;
        }
        
        .page-header {
            padding: 40px 30px 30px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .page-title {
            font-size: 24px;
            font-weight: bold;
            color: #ffffff;
            margin-bottom: 8px;
        }
        
        .page-subtitle {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .filter-tabs {
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            margin: 20px 0;
            border-radius: 25px;
            padding: 8px;
            box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
        }
        
        .tab-item {
            flex: 1;
            text-align: center;
            padding: 15px 10px;
            border-radius: 20px;
            position: relative;
            transition: all 0.3s ease;
            cursor: pointer;
            font-size: 14px;
            color: #666;
            font-weight: 500;
        }
        
        .tab-item.active {
            background: linear-gradient(135deg, #667eea, #764ba2);
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            color: white;
            font-weight: bold;
        }
        
        .badge {
            position: absolute;
            top: 8px;
            right: 12px;
            background: #ff4757;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            min-width: 16px;
            text-align: center;
        }
        
        .tab-item.active .badge {
            background: #ff6b7a;
        }
        
        .record-item {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            margin-bottom: 15px;
            overflow: hidden;
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .record-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 12px 30px rgba(0, 0, 0, 0.15);
        }
        
        .record-card {
            padding: 20px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .prize-info {
            display: flex;
            align-items: center;
            flex: 1;
        }
        
        .prize-icon {
            width: 60px;
            height: 60px;
            margin-right: 15px;
            border-radius: 12px;
            overflow: hidden;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
        }
        
        .prize-details {
            flex: 1;
        }
        
        .prize-name {
            font-size: 16px;
            font-weight: bold;
            color: #333;
            margin-bottom: 5px;
        }
        
        .prize-desc {
            font-size: 12px;
            color: #666;
            margin-bottom: 5px;
            line-height: 1.4;
        }
        
        .draw-time {
            font-size: 12px;
            color: #999;
        }
        
        .status-section {
            display: flex;
            flex-direction: column;
            align-items: flex-end;
            gap: 10px;
        }
        
        .status-badge {
            padding: 6px 12px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status-badge.claimed {
            background: linear-gradient(135deg, #2ed573, #7bed9f);
            color: white;
        }
        
        .status-badge.unclaimed {
            background: linear-gradient(135deg, #ffa726, #ffcc02);
            color: white;
            animation: pulse 2s infinite;
        }
        
        .status-badge.not-won {
            background: #f1f2f6;
            color: #666;
        }
        
        .claim-btn {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 8px 16px;
            border-radius: 15px;
            font-size: 12px;
            font-weight: bold;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
        }
        
        .claim-btn:hover {
            transform: scale(0.95);
            box-shadow: 0 2px 10px rgba(102, 126, 234, 0.6);
        }
        
        @keyframes pulse {
            0% {
                box-shadow: 0 0 0 0 rgba(255, 167, 38, 0.7);
            }
            70% {
                box-shadow: 0 0 0 8px rgba(255, 167, 38, 0);
            }
            100% {
                box-shadow: 0 0 0 0 rgba(255, 167, 38, 0);
            }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 30px;
            color: rgba(255, 255, 255, 0.8);
        }
        
        .empty-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 10px;
        }
        
        .empty-subtitle {
            font-size: 14px;
            margin-bottom: 30px;
        }
        
        .btn-primary {
            background: linear-gradient(135deg, #ff6b7a, #ff8e9b);
            color: white;
            padding: 12px 30px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: bold;
            box-shadow: 0 8px 25px rgba(255, 107, 122, 0.4);
            border: none;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="records-page">
        <!-- 页面头部 -->
        <div class="page-header">
            <div class="page-title">我的中奖记录</div>
            <div class="page-subtitle">查看您的抽奖历史和中奖情况</div>
        </div>

        <!-- 筛选标签 -->
        <div class="filter-tabs">
            <div class="tab-item active" onclick="switchTab('all')">全部记录</div>
            <div class="tab-item" onclick="switchTab('winning')">中奖记录</div>
            <div class="tab-item" onclick="switchTab('unclaimed')">
                待领取
                <div class="badge">2</div>
            </div>
        </div>

        <!-- 记录列表 -->
        <div class="records-container">
            <!-- 示例记录 1 - 待领取 -->
            <div class="record-item">
                <div class="record-card">
                    <div class="prize-info">
                        <div class="prize-icon">🎁</div>
                        <div class="prize-details">
                            <div class="prize-name">一等奖 - 100元代金券</div>
                            <div class="prize-desc">可在本店消费时使用，有效期30天</div>
                            <div class="draw-time">2024-01-15 14:30</div>
                        </div>
                    </div>
                    <div class="status-section">
                        <div class="status-badge unclaimed">待领取</div>
                        <button class="claim-btn" onclick="claimPrize('record1')">立即领取</button>
                    </div>
                </div>
            </div>

            <!-- 示例记录 2 - 已领取 -->
            <div class="record-item">
                <div class="record-card">
                    <div class="prize-info">
                        <div class="prize-icon">🏆</div>
                        <div class="prize-details">
                            <div class="prize-name">二等奖 - 精美礼品</div>
                            <div class="prize-desc">限量版纪念品一份</div>
                            <div class="draw-time">2024-01-14 16:45</div>
                        </div>
                    </div>
                    <div class="status-section">
                        <div class="status-badge claimed">已领取</div>
                    </div>
                </div>
            </div>

            <!-- 示例记录 3 - 待领取 -->
            <div class="record-item">
                <div class="record-card">
                    <div class="prize-info">
                        <div class="prize-icon">🎊</div>
                        <div class="prize-details">
                            <div class="prize-name">三等奖 - 免费饮品</div>
                            <div class="prize-desc">任选店内饮品一杯</div>
                            <div class="draw-time">2024-01-13 12:20</div>
                        </div>
                    </div>
                    <div class="status-section">
                        <div class="status-badge unclaimed">待领取</div>
                        <button class="claim-btn" onclick="claimPrize('record3')">立即领取</button>
                    </div>
                </div>
            </div>

            <!-- 示例记录 4 - 未中奖 -->
            <div class="record-item">
                <div class="record-card">
                    <div class="prize-info">
                        <div class="prize-icon">😔</div>
                        <div class="prize-details">
                            <div class="prize-name">谢谢参与</div>
                            <div class="prize-desc">很遗憾，这次没有中奖</div>
                            <div class="draw-time">2024-01-12 18:15</div>
                        </div>
                    </div>
                    <div class="status-section">
                        <div class="status-badge not-won">未中奖</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        function switchTab(tab) {
            // 移除所有active类
            document.querySelectorAll('.tab-item').forEach(item => {
                item.classList.remove('active');
            });
            
            // 添加active类到点击的标签
            event.target.classList.add('active');
            
            // 这里可以添加筛选逻辑
            console.log('切换到标签:', tab);
        }
        
        function claimPrize(recordId) {
            alert('跳转到领取页面: ' + recordId);
            // 实际项目中这里会跳转到领取页面
        }
    </script>
</body>
</html>
