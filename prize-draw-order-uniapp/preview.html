<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>首页预览 - 遮罩层效果</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding-bottom: 80px;
        }

        .container {
            min-height: 100vh;
            position: relative;
            display: flex;
            flex-direction: column;
            padding: 20px 15px 80px;
        }

        .main-content {
            flex: 1;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: flex-start;
            z-index: 2;
            position: relative;
        }

        .logo-container {
            margin-bottom: 15px;
        }

        .logo-img {
            width: 60px;
            height: 60px;
            border-radius: 30px;
            background: rgba(255, 255, 255, 0.9);
            padding: 5px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .merchant-info {
            text-align: center;
            margin: 15px 0;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(5px);
            border-radius: 10px;
            padding: 15px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
        }

        .merchant-name {
            font-size: 21px;
            font-weight: bold;
            color: #333;
            margin-bottom: 8px;
        }

        .merchant-address {
            font-size: 14px;
            color: #666;
            margin-bottom: 8px;
        }

        .table-number {
            font-size: 14px;
            color: #888;
            background: rgba(102, 126, 234, 0.1);
            padding: 8px 15px;
            border-radius: 25px;
            display: inline-block;
        }

        /* 固定在底部的功能按钮 */
        .fixed-bottom-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            display: flex;
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(5px);
            padding: 10px 15px;
            box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
            z-index: 100;
            gap: 10px;
        }

        .function-btn {
            flex: 1;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 12px;
            padding: 15px 10px;
            text-align: center;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
            transition: all 0.3s ease;
            cursor: pointer;
            border: none;
            color: white;
        }

        .function-btn:active {
            transform: scale(0.95);
        }

        .function-btn.disabled {
            background: linear-gradient(135deg, #ccc 0%, #999 100%);
            box-shadow: 0 2px 8px rgba(153, 153, 153, 0.3);
        }

        .function-btn.disabled .btn-text {
            color: #666;
        }

        .btn-icon {
            font-size: 25px;
            margin-bottom: 5px;
        }

        .btn-text {
            font-size: 14px;
            font-weight: bold;
            color: #fff;
        }

        .order-btn {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
            box-shadow: 0 2px 8px rgba(255, 154, 158, 0.3);
        }

        .order-btn.disabled {
            background: linear-gradient(135deg, #ccc 0%, #999 100%);
            box-shadow: 0 2px 8px rgba(153, 153, 153, 0.3);
        }

        .lottery-btn {
            background: linear-gradient(135deg, #a8edea 0%, #fed6e3 100%);
            box-shadow: 0 2px 8px rgba(168, 237, 234, 0.3);
        }

        /* 欢迎遮罩层 */
        .welcome-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.8);
            backdrop-filter: blur(5px);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 1000;
            padding: 20px;
        }

        .overlay-content {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 15px;
            padding: 30px 20px;
            max-width: 300px;
            width: 100%;
            text-align: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.3);
        }

        .close-btn {
            position: absolute;
            top: 10px;
            right: 10px;
            width: 30px;
            height: 30px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .close-btn:active {
            transform: scale(0.9);
            background: rgba(255, 255, 255, 0.3);
        }

        .close-icon {
            color: #fff;
            font-size: 16px;
            font-weight: bold;
        }

        .overlay-welcome-text {
            font-size: 21px;
            color: #fff;
            font-weight: bold;
            margin-bottom: 20px;
            text-shadow: 0 1px 4px rgba(0, 0, 0, 0.3);
            line-height: 1.4;
        }

        .overlay-image-container {
            margin-bottom: 20px;
        }

        .overlay-bg-image {
            width: 100%;
            max-width: 200px;
            height: 150px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
            object-fit: cover;
        }

        .overlay-countdown {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.8);
            margin-top: 10px;
        }

        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <!-- 主要内容区域 -->
        <div class="main-content">
            <!-- Logo图片 -->
            <div class="logo-container">
                <img src="https://via.placeholder.com/60x60/667eea/ffffff?text=LOGO" class="logo-img" alt="Logo">
            </div>

            <!-- 商家信息 -->
            <div class="merchant-info">
                <div class="merchant-name">示例餐厅</div>
                <div class="merchant-address">北京市朝阳区示例街道123号</div>
                <div class="table-info">
                    <div class="table-number">桌台：A002</div>
                </div>
            </div>
        </div>

        <!-- 固定在底部的功能按钮 -->
        <div class="fixed-bottom-buttons">
            <button class="function-btn order-btn" id="orderBtn">
                <div class="btn-icon">🍽️</div>
                <div class="btn-text" id="orderText">点餐(5s)</div>
            </button>
            <button class="function-btn lottery-btn">
                <div class="btn-icon">🎁</div>
                <div class="btn-text">抽奖</div>
            </button>
        </div>

        <!-- 欢迎遮罩层 -->
        <div class="welcome-overlay" id="welcomeOverlay">
            <div class="overlay-content">
                <!-- 关闭按钮 -->
                <div class="close-btn" onclick="closeWelcomeOverlay()">
                    <span class="close-icon">✕</span>
                </div>
                
                <!-- 欢迎语 -->
                <div class="overlay-welcome-text">
                    欢迎来到示例餐厅！<br>享受美食，体验抽奖乐趣！
                </div>
                
                <!-- 背景图片 -->
                <div class="overlay-image-container">
                    <img src="https://via.placeholder.com/200x150/ff9a9e/ffffff?text=Welcome" class="overlay-bg-image" alt="欢迎图片">
                </div>
                
                <!-- 倒计时提示 -->
                <div class="overlay-countdown" id="overlayCountdown">10秒后自动关闭</div>
            </div>
        </div>
    </div>

    <script>
        let overlayTimer = null;
        let overlayCountdown = 10;
        let orderButtonEnabled = false;
        let orderCountdown = 5;
        let orderTimer = null;

        // 显示欢迎遮罩层并启动倒计时
        function showWelcomeOverlayWithTimer() {
            const overlay = document.getElementById('welcomeOverlay');
            const countdownElement = document.getElementById('overlayCountdown');
            
            overlay.classList.remove('hidden');
            overlayCountdown = 10;
            
            overlayTimer = setInterval(() => {
                overlayCountdown--;
                countdownElement.textContent = overlayCountdown + '秒后自动关闭';
                if (overlayCountdown <= 0) {
                    closeWelcomeOverlay();
                }
            }, 1000);
        }

        // 关闭欢迎遮罩层
        function closeWelcomeOverlay() {
            const overlay = document.getElementById('welcomeOverlay');
            overlay.classList.add('hidden');
            if (overlayTimer) {
                clearInterval(overlayTimer);
                overlayTimer = null;
            }
        }

        // 点餐按钮倒计时
        function startOrderButtonCountdown() {
            const orderBtn = document.getElementById('orderBtn');
            const orderText = document.getElementById('orderText');
            
            orderButtonEnabled = false;
            orderCountdown = 5;
            orderBtn.classList.add('disabled');
            
            orderTimer = setInterval(() => {
                orderCountdown--;
                orderText.textContent = orderCountdown > 0 ? `点餐(${orderCountdown}s)` : '点餐';
                if (orderCountdown <= 0) {
                    orderButtonEnabled = true;
                    orderBtn.classList.remove('disabled');
                    clearInterval(orderTimer);
                    orderTimer = null;
                }
            }, 1000);
        }

        // 点击遮罩层背景关闭
        document.getElementById('welcomeOverlay').addEventListener('click', function(e) {
            if (e.target === this) {
                closeWelcomeOverlay();
            }
        });

        // 页面加载完成后启动
        window.addEventListener('load', function() {
            setTimeout(() => {
                showWelcomeOverlayWithTimer();
            }, 500);
            startOrderButtonCountdown();
        });
    </script>
</body>
</html>
