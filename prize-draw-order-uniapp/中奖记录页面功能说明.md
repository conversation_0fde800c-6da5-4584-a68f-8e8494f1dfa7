# 中奖记录页面功能说明

## 功能概述

中奖记录页面用于展示用户的抽奖历史和中奖情况，支持多种筛选方式和便捷的奖品领取功能。

## 页面特性

### 1. 页面布局
- **渐变背景**：采用紫色渐变背景，视觉效果佳
- **响应式设计**：适配不同屏幕尺寸
- **卡片式布局**：每条记录以卡片形式展示，层次分明

### 2. 筛选功能
- **全部记录**：显示用户所有的抽奖记录
- **中奖记录**：只显示中奖的记录
- **待领取**：显示未领取的中奖记录，并显示数量徽章

### 3. 记录展示
每条记录包含以下信息：
- **奖品图标**：支持自定义奖品图片或默认图标
- **奖品名称**：奖品的名称
- **奖品描述**：奖品的详细描述（可选）
- **抽奖时间**：格式化显示的抽奖时间
- **状态标签**：已领取/待领取/未中奖
- **操作按钮**：待领取奖品显示"立即领取"按钮

### 4. 交互功能
- **下拉刷新**：支持下拉刷新数据
- **点击跳转**：点击待领取记录跳转到领取页面
- **详情查看**：点击其他记录显示详情弹窗
- **标签切换**：点击标签切换不同的记录视图

## 文件结构

```
pages/records/
└── records.vue          # 中奖记录页面主文件
```

## 页面配置

在 `pages.json` 中的配置：

```json
{
  "path": "pages/records/records",
  "style": {
    "navigationBarTitleText": "中奖记录",
    "navigationBarBackgroundColor": "#667eea",
    "navigationBarTextStyle": "white",
    "enablePullDownRefresh": true,
    "backgroundTextStyle": "light"
  }
}
```

## API 接口

页面使用以下API接口：

### 1. 获取用户全部记录
```javascript
lotteryApi.getUserRecords(userOpenid)
```

### 2. 获取用户中奖记录
```javascript
lotteryApi.getUserWinningRecords(userOpenid)
```

### 3. 获取用户未领取记录
```javascript
lotteryApi.getUserUnclaimedRecords(userOpenid)
```

## 数据结构

### 记录数据结构
```javascript
{
  recordId: "record_123",           // 记录ID
  prizeName: "一等奖",              // 奖品名称
  prizeDesc: "100元代金券",         // 奖品描述
  prizeImage: "/static/prize1.jpg", // 奖品图片
  drawTime: "2024-01-15T14:30:00Z", // 抽奖时间
  isWinner: "1",                    // 是否中奖：'1'-中奖，'0'-未中奖
  claimStatus: "0",                 // 领取状态：'0'-待领取，'1'-已领取
  activityName: "新年抽奖活动"       // 活动名称（可选）
}
```

## 状态管理

### 页面状态
- `isLoading`: 页面加载状态
- `isRefreshing`: 下拉刷新状态
- `currentTab`: 当前选中的标签页

### 数据状态
- `allRecords`: 全部记录数组
- `winningRecords`: 中奖记录数组
- `unclaimedRecords`: 未领取记录数组
- `unclaimedCount`: 未领取数量

## 样式特性

### 1. 视觉效果
- **渐变背景**：紫色渐变背景
- **毛玻璃效果**：头部使用backdrop-filter
- **卡片阴影**：记录卡片带有阴影效果
- **动画效果**：点击和悬停动画

### 2. 状态标识
- **已领取**：绿色渐变标签
- **待领取**：橙色渐变标签，带脉冲动画
- **未中奖**：灰色标签

### 3. 响应式设计
- 适配不同屏幕尺寸
- 字体大小自适应
- 图标大小自适应

## 使用方法

### 1. 从抽奖页面跳转
```javascript
// 在抽奖页面中
viewAllRecords() {
  uni.navigateTo({
    url: `/pages/records/records?userOpenid=${this.userOpenid}`
  })
}
```

### 2. 直接访问
```javascript
uni.navigateTo({
  url: '/pages/records/records'
})
```

## 预览页面

创建了 `records-preview.html` 文件用于在浏览器中预览页面效果：

```bash
# 在浏览器中打开
file:///path/to/prize-draw-order-uniapp/records-preview.html
```

## 注意事项

1. **用户身份识别**：页面需要获取用户openid来加载对应的记录
2. **图片加载**：奖品图片支持相对路径和完整URL
3. **错误处理**：网络请求失败时显示友好提示
4. **空状态处理**：无记录时显示空状态页面
5. **性能优化**：大量记录时可考虑分页加载

## 扩展功能

可以考虑添加的功能：
- 记录搜索功能
- 按时间筛选
- 记录导出功能
- 分享中奖记录
- 记录统计图表

## 测试建议

1. **功能测试**：测试各种筛选条件下的数据显示
2. **交互测试**：测试点击、下拉刷新等交互功能
3. **边界测试**：测试无数据、网络异常等边界情况
4. **性能测试**：测试大量数据时的页面性能
5. **兼容性测试**：测试不同设备和平台的兼容性
