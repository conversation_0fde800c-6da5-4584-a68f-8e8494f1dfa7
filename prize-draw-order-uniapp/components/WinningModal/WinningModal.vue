<template>
  <view class="winning-modal-overlay" v-if="visible" @click="handleOverlayClick">
    <view class="winning-modal" @click.stop>
      <!-- 关闭按钮 -->
      <view class="close-btn" @click="closeModal">
        <text class="close-icon">×</text>
      </view>

      <!-- 中奖动画效果 -->
      <view class="winning-animation">
        <view class="fireworks">
          <view class="firework" v-for="n in 6" :key="n" :style="getFireworkStyle(n)"></view>
        </view>

        <!-- 中奖图标 -->
        <view class="winning-icon">
          <text class="icon-text">🎉</text>
        </view>

        <!-- 恭喜文字 -->
        <view class="congratulations">
          <text class="congrats-text">恭喜中奖！</text>
        </view>

        <!-- 奖品信息 -->
        <view class="prize-info">
          <view class="prize-name">{{ result.prizeName }}</view>
          <view class="prize-desc" v-if="result.prizeDesc">{{ result.prizeDesc }}</view>
        </view>

        <!-- 中奖时间 -->
        <view class="draw-time">
          <text>抽奖时间：{{ formatTime(result.drawTime) }}</text>
        </view>

        <!-- 领取说明 -->
        <view class="claim-instruction" v-if="activityInfo && activityInfo.claimInstruction">
          <view class="instruction-title">领取说明</view>
          <view class="instruction-content">{{ activityInfo.claimInstruction }}</view>
        </view>

        <!-- 微信二维码 -->
        <view class="wechat-qrcode" v-if="activityInfo && activityInfo.wechatQrcode">
          <image :src="getFullImageUrl(activityInfo.wechatQrcode)" class="qrcode-img" mode="aspectFit"
            @error="handleImageError" @load="handleImageLoad"></image>
          <view class="qrcode-error" v-if="imageLoadError">
            <text>二维码加载失败</text>
          </view>
        </view>

        <!-- 操作按钮 -->
        <view class="action-buttons">
          <view class="btn btn-secondary" @click="closeModal">
            <text>稍后领取</text>
          </view>
          <view class="btn btn-primary" @click="goToClaim" v-if="result.isWinner === '1'">
            <text>立即领取</text>
          </view>
        </view>
      </view>

      <!-- 自动关闭倒计时 -->
      <view class="countdown" v-if="countdown > 0">
        <text>{{ countdown }}秒后自动关闭</text>
      </view>
    </view>
  </view>
</template>

<script>
import { getImageUrl } from '@/utils/api.js'

export default {
  name: 'WinningModal',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    result: {
      type: Object,
      default: () => ({})
    },
    activityInfo: {
      type: Object,
      default: () => ({})
    },
    autoClose: {
      type: Boolean,
      default: true
    },
    autoCloseDelay: {
      type: Number,
      default: 5000 // 5秒
    }
  },
  data() {
    return {
      countdown: 0,
      timer: null,
      countdownTimer: null,
      imageLoadError: false
    }
  },
  watch: {
    visible(newVal) {
      if (newVal) {
        this.startAutoClose()
      } else {
        this.clearTimers()
      }
    }
  },
  methods: {
    startAutoClose() {
      if (!this.autoClose) return

      this.countdown = Math.ceil(this.autoCloseDelay / 1000)

      // 倒计时显示
      this.countdownTimer = setInterval(() => {
        this.countdown--
        if (this.countdown <= 0) {
          clearInterval(this.countdownTimer)
        }
      }, 1000)

      // 自动关闭定时器
      this.timer = setTimeout(() => {
        this.closeModal()
      }, this.autoCloseDelay)
    },

    clearTimers() {
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      if (this.countdownTimer) {
        clearInterval(this.countdownTimer)
        this.countdownTimer = null
      }
      this.countdown = 0
    },

    closeModal() {
      this.clearTimers()
      this.$emit('close')
    },

    handleOverlayClick() {
      // 点击遮罩层关闭弹窗
      this.closeModal()
    },

    goToClaim() {
      this.closeModal()
      this.$emit('claim', this.result)
    },

    formatTime(timeStr) {
      if (!timeStr) return ''
      const date = new Date(timeStr)
      return `${date.getMonth() + 1}-${date.getDate()} ${date.getHours()}:${date.getMinutes().toString().padStart(2, '0')}`
    },

    getFireworkStyle(index) {
      const angles = [0, 60, 120, 180, 240, 300]
      const angle = angles[index - 1]
      return {
        transform: `rotate(${angle}deg)`,
        animationDelay: `${index * 0.1}s`
      }
    },

    // 获取完整的图片URL
    getFullImageUrl(imagePath) {
      return getImageUrl(imagePath)
    },

    // 图片加载错误处理
    handleImageError() {
      console.error('微信二维码图片加载失败')
      this.imageLoadError = true
    },

    // 图片加载成功处理
    handleImageLoad() {
      this.imageLoadError = false
    }
  },

  beforeDestroy() {
    this.clearTimers()
  }
}
</script>

<style lang="scss" scoped>
.winning-modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  animation: fadeIn 0.3s ease-out;
}

.winning-modal {
  background: linear-gradient(135deg, #ff6b6b, #ffd93d);
  border-radius: 20rpx;
  padding: 60rpx 40rpx 40rpx;
  margin: 40rpx;
  max-width: 600rpx;
  width: 90%;
  position: relative;
  animation: modalSlideIn 0.4s ease-out;
  box-shadow: 0 20rpx 60rpx rgba(0, 0, 0, 0.3);
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 10;
}

.close-icon {
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
}

.winning-animation {
  text-align: center;
  position: relative;
}

.fireworks {
  position: absolute;
  top: -20rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 200rpx;
  height: 200rpx;
}

.firework {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8rpx;
  height: 8rpx;
  background: #fff;
  border-radius: 50%;
  animation: fireworkAnimation 1.5s ease-out infinite;
  transform-origin: 0 0;
}

.winning-icon {
  margin: 40rpx 0 20rpx;
  animation: bounce 1s ease-in-out infinite;
}

.icon-text {
  font-size: 120rpx;
  line-height: 1;
}

.congratulations {
  margin-bottom: 30rpx;
}

.congrats-text {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  text-shadow: 2rpx 2rpx 4rpx rgba(0, 0, 0, 0.3);
}

.prize-info {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}

.prize-name {
  color: #333;
  font-size: 36rpx;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.prize-desc {
  color: #666;
  font-size: 28rpx;
}

.draw-time {
  margin: 20rpx 0 40rpx;
}

.draw-time text {
  color: rgba(255, 255, 255, 0.8);
  font-size: 24rpx;
}

.claim-instruction {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
}

.instruction-title {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 15rpx;
}

.instruction-content {
  color: #666;
  font-size: 26rpx;
  line-height: 1.5;
}

.wechat-qrcode {
  background: rgba(255, 255, 255, 0.9);
  border-radius: 15rpx;
  padding: 30rpx;
  margin: 30rpx 0;
  text-align: center;
}

.qrcode-title {
  color: #333;
  font-size: 30rpx;
  font-weight: bold;
  margin-bottom: 20rpx;
}

.qrcode-img {
  width: 200rpx;
  height: 200rpx;
  border-radius: 10rpx;
  margin-bottom: 15rpx;
}

.qrcode-desc {
  color: #666;
  font-size: 24rpx;
  line-height: 1.4;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
  justify-content: center;
}

.btn {
  flex: 1;
  padding: 24rpx 40rpx;
  border-radius: 50rpx;
  text-align: center;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.3s ease;
}

.btn-secondary {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 2rpx solid rgba(255, 255, 255, 0.3);
}

.btn-primary {
  background: #fff;
  color: #ff6b6b;
  box-shadow: 0 8rpx 20rpx rgba(0, 0, 0, 0.2);
}

.countdown {
  text-align: center;
  margin-top: 20rpx;
}

.countdown text {
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
  }

  to {
    opacity: 1;
  }
}

@keyframes modalSlideIn {
  from {
    transform: scale(0.8) translateY(-50rpx);
    opacity: 0;
  }

  to {
    transform: scale(1) translateY(0);
    opacity: 1;
  }
}

@keyframes bounce {

  0%,
  20%,
  50%,
  80%,
  100% {
    transform: translateY(0);
  }

  40% {
    transform: translateY(-20rpx);
  }

  60% {
    transform: translateY(-10rpx);
  }
}

@keyframes fireworkAnimation {
  0% {
    transform: translate(0, 0) scale(1);
    opacity: 1;
  }

  100% {
    transform: translate(100rpx, -100rpx) scale(0);
    opacity: 0;
  }
}
</style>
