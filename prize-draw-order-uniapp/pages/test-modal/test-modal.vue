<template>
  <view class="test-container">
    <view class="header">
      <text class="title">中奖弹窗测试页面</text>
    </view>

    <view class="test-buttons">
      <view class="test-btn" @click="showWinningModal">
        <text>测试中奖弹窗</text>
      </view>

      <view class="test-btn" @click="showNoWinModal">
        <text>测试未中奖弹窗</text>
      </view>
    </view>

    <!-- 中奖弹窗 -->
    <WinningModal :visible="modalVisible" :result="testResult" :activityInfo="testActivityInfo" :autoClose="true"
      :autoCloseDelay="5000" @close="handleModalClose" @claim="handleClaimPrize" />
  </view>
</template>

<script>
import WinningModal from '@/components/WinningModal/WinningModal.vue'
import { configApi } from '@/utils/api.js'

export default {
  components: {
    WinningModal
  },

  data() {
    return {
      modalVisible: false,
      testResult: null,
      testActivityInfo: {
        activityName: '新年抽奖活动',
        claimInstruction: '请携带本人身份证到前台领取奖品，领取时间：周一至周日 9:00-18:00。如有疑问请联系客服：400-123-4567',
        wechatQrcode: 'https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=WeChat+QR'
      },
      merchantCode: '002',
      merchantConfig: {}
    }
  },

  computed: {
    // 解析UI配置
    uiConfig() {
      const uiConfigStr = this.merchantConfig.ui_config
      if (uiConfigStr) {
        try {
          return JSON.parse(uiConfigStr)
        } catch (e) {
          console.error('UI配置解析失败:', e)
          return {}
        }
      }
      return {}
    },

    // 主题色彩
    primaryColor() {
      return this.uiConfig.primaryColor || '#667eea'
    }
  },

  watch: {
    // 监听主题色彩变化，动态设置导航栏颜色
    primaryColor: {
      handler(newColor) {
        if (newColor) {
          uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: newColor
          })
        }
      },
      immediate: true
    }
  },

  onLoad() {
    this.initPage()
  },

  methods: {
    async initPage() {
      try {
        // 加载商家信息
        await this.loadMerchantInfo()
      } catch (error) {
        console.error('页面初始化失败:', error)
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await configApi.getMerchantConfig(this.merchantCode)
        if (res.code === 200) {
          this.merchantConfig = res.data || {}
        }
      } catch (error) {
        console.error('获取商家配置失败:', error)
        this.merchantConfig = {}
      }
    },
    showWinningModal() {
      this.testResult = {
        isWinner: '1',
        prizeName: '精美礼品一份',
        prizeDesc: '恭喜您获得了精美礼品，请到前台领取！',
        drawTime: new Date().toISOString(),
        recordId: 'test_record_001',
        claimStatus: '0'
      }
      this.modalVisible = true
    },

    showNoWinModal() {
      this.testResult = {
        isWinner: '0',
        prizeName: '谢谢参与',
        drawTime: new Date().toISOString(),
        recordId: 'test_record_002'
      }
      this.modalVisible = true
    },

    handleModalClose() {
      this.modalVisible = false
      console.log('弹窗已关闭')
    },

    handleClaimPrize(result) {
      console.log('领取奖品:', result)
      uni.showToast({
        title: '跳转到领取页面',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  padding: 60rpx 40rpx;
}

.header {
  text-align: center;
  margin-bottom: 80rpx;
}

.title {
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
}

.test-buttons {
  display: flex;
  flex-direction: column;
  gap: 40rpx;
  align-items: center;
}

.test-btn {
  background: rgba(255, 255, 255, 0.9);
  padding: 30rpx 60rpx;
  border-radius: 50rpx;
  box-shadow: 0 10rpx 30rpx rgba(0, 0, 0, 0.2);
}

.test-btn text {
  color: #333;
  font-size: 32rpx;
  font-weight: bold;
}
</style>
