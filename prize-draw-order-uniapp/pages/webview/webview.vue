<template>
  <view class="webview-container">
    <web-view :src="url" @message="handleMessage"></web-view>
  </view>
</template>

<script>
import { configApi } from '@/utils/api.js'

export default {
  data() {
    return {
      url: '',
      merchantCode: '',
      merchantConfig: {}
    }
  },

  computed: {
    // 解析UI配置
    uiConfig() {
      const uiConfigStr = this.merchantConfig.ui_config
      if (uiConfigStr) {
        try {
          return JSON.parse(uiConfigStr)
        } catch (e) {
          console.error('UI配置解析失败:', e)
          return {}
        }
      }
      return {}
    },

    // 主题色彩
    primaryColor() {
      return this.uiConfig.primaryColor || '#667eea'
    }
  },

  watch: {
    // 监听主题色彩变化，动态设置导航栏颜色
    primaryColor: {
      handler(newColor) {
        if (newColor) {
          uni.setNavigationBarColor({
            frontColor: '#ffffff',
            backgroundColor: newColor
          })
        }
      },
      immediate: true
    }
  },

  onLoad(options) {
    this.url = decodeURIComponent(options.url || '')
    this.merchantCode = options.merchantCode || '002'

    this.initPage()
  },

  methods: {
    async initPage() {
      try {
        // 加载商家信息
        await this.loadMerchantInfo()

        // 设置导航栏标题
        uni.setNavigationBarTitle({
          title: '点餐'
        })
      } catch (error) {
        console.error('页面初始化失败:', error)
      }
    },

    async loadMerchantInfo() {
      try {
        const res = await configApi.getMerchantConfig(this.merchantCode)
        if (res.code === 200) {
          this.merchantConfig = res.data || {}
        }
      } catch (error) {
        console.error('获取商家配置失败:', error)
        this.merchantConfig = {}
      }
    },

    handleMessage(event) {
      console.log('WebView消息:', event.detail.data)
    }
  }
}
</script>

<style lang="scss" scoped>
.webview-container {
  height: 100vh;
}
</style>
