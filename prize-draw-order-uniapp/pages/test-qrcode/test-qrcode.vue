<template>
  <view class="test-container">
    <view class="header">
      <text class="title">微信二维码测试页面</text>
    </view>

    <view class="test-section">
      <view class="section-title">测试不同类型的图片URL</view>
      
      <!-- 测试完整URL -->
      <view class="test-item">
        <view class="test-label">完整URL测试:</view>
        <view class="qrcode-container">
          <image 
            :src="getFullImageUrl('https://via.placeholder.com/200x200/4CAF50/FFFFFF?text=QR+Code')" 
            class="qrcode-img" 
            mode="aspectFit"
            @error="handleImageError"
            @load="handleImageLoad"
          ></image>
          <view class="test-result" :class="{ 'error': imageLoadError1 }">
            {{ imageLoadError1 ? '加载失败' : '加载成功' }}
          </view>
        </view>
      </view>

      <!-- 测试相对路径 -->
      <view class="test-item">
        <view class="test-label">相对路径测试:</view>
        <view class="qrcode-container">
          <image 
            :src="getFullImageUrl('/static/images/test-qrcode.png')" 
            class="qrcode-img" 
            mode="aspectFit"
            @error="handleImageError2"
            @load="handleImageLoad2"
          ></image>
          <view class="test-result" :class="{ 'error': imageLoadError2 }">
            {{ imageLoadError2 ? '加载失败' : '加载成功' }}
          </view>
        </view>
      </view>

      <!-- 测试错误URL -->
      <view class="test-item">
        <view class="test-label">错误URL测试:</view>
        <view class="qrcode-container">
          <image 
            :src="getFullImageUrl('https://invalid-url-test.com/nonexistent.jpg')" 
            class="qrcode-img" 
            mode="aspectFit"
            @error="handleImageError3"
            @load="handleImageLoad3"
          ></image>
          <view class="test-result" :class="{ 'error': imageLoadError3 }">
            {{ imageLoadError3 ? '加载失败' : '加载成功' }}
          </view>
        </view>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">WinningModal组件测试</view>
      <view class="btn-container">
        <view class="test-btn" @click="showWinningModal">
          <text>显示中奖弹窗</text>
        </view>
      </view>
    </view>

    <view class="test-section">
      <view class="section-title">调试信息</view>
      <view class="debug-info">
        <view class="debug-item">
          <text class="debug-label">API_BASE_URL:</text>
          <text class="debug-value">{{ apiBaseUrl }}</text>
        </view>
        <view class="debug-item">
          <text class="debug-label">测试URL处理:</text>
          <text class="debug-value">{{ testUrlProcessing }}</text>
        </view>
      </view>
    </view>

    <!-- 中奖弹窗 -->
    <WinningModal 
      :visible="showModal" 
      :result="testResult" 
      :activityInfo="testActivity"
      :autoClose="false"
      @close="handleModalClose" 
      @claim="handleClaimPrize" 
    />
  </view>
</template>

<script>
import { getImageUrl } from '@/utils/api.js'
import WinningModal from '@/components/WinningModal/WinningModal.vue'

export default {
  components: {
    WinningModal
  },
  data() {
    return {
      imageLoadError1: false,
      imageLoadError2: false,
      imageLoadError3: false,
      showModal: false,
      testResult: {
        isWinner: '1',
        prizeName: '测试奖品',
        prizeDesc: '这是一个测试奖品描述',
        drawTime: new Date().toISOString(),
        recordId: 'test_record_123',
        claimStatus: '0'
      },
      testActivity: {
        activityName: '测试活动',
        claimInstruction: '请携带身份证到前台领取奖品，工作时间：9:00-18:00',
        wechatQrcode: 'https://via.placeholder.com/200x200/2196F3/FFFFFF?text=WeChat+QR'
      }
    }
  },
  computed: {
    apiBaseUrl() {
      // 从api.js中获取BASE_URL用于调试
      return 'http://localhost:18080'
    },
    testUrlProcessing() {
      const testPath = '/test/image.jpg'
      return this.getFullImageUrl(testPath)
    }
  },
  methods: {
    getFullImageUrl(imagePath) {
      const fullUrl = getImageUrl(imagePath)
      console.log('测试页面 - 原始路径:', imagePath)
      console.log('测试页面 - 处理后URL:', fullUrl)
      return fullUrl
    },

    handleImageError(event) {
      console.error('图片1加载失败:', event)
      this.imageLoadError1 = true
    },

    handleImageLoad(event) {
      console.log('图片1加载成功:', event)
      this.imageLoadError1 = false
    },

    handleImageError2(event) {
      console.error('图片2加载失败:', event)
      this.imageLoadError2 = true
    },

    handleImageLoad2(event) {
      console.log('图片2加载成功:', event)
      this.imageLoadError2 = false
    },

    handleImageError3(event) {
      console.error('图片3加载失败:', event)
      this.imageLoadError3 = true
    },

    handleImageLoad3(event) {
      console.log('图片3加载成功:', event)
      this.imageLoadError3 = false
    },

    showWinningModal() {
      this.showModal = true
    },

    handleModalClose() {
      this.showModal = false
    },

    handleClaimPrize(result) {
      console.log('领取奖品:', result)
      this.showModal = false
      uni.showToast({
        title: '跳转到领取页面',
        icon: 'success'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.test-container {
  padding: 30rpx;
  background: #f5f5f5;
  min-height: 100vh;
}

.header {
  text-align: center;
  margin-bottom: 40rpx;
}

.title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
}

.test-section {
  background: white;
  border-radius: 15rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  border-bottom: 2rpx solid #eee;
  padding-bottom: 15rpx;
}

.test-item {
  margin-bottom: 40rpx;
}

.test-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.qrcode-container {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

.qrcode-img {
  width: 120rpx;
  height: 120rpx;
  border-radius: 10rpx;
  border: 2rpx solid #eee;
}

.test-result {
  font-size: 26rpx;
  color: #4CAF50;
  font-weight: bold;
  
  &.error {
    color: #ff4757;
  }
}

.btn-container {
  text-align: center;
}

.test-btn {
  display: inline-block;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  padding: 20rpx 40rpx;
  border-radius: 25rpx;
  font-size: 28rpx;
  font-weight: bold;
}

.debug-info {
  background: #f8f9fa;
  border-radius: 10rpx;
  padding: 20rpx;
}

.debug-item {
  display: flex;
  margin-bottom: 15rpx;
  
  &:last-child {
    margin-bottom: 0;
  }
}

.debug-label {
  font-size: 26rpx;
  color: #666;
  width: 200rpx;
  flex-shrink: 0;
}

.debug-value {
  font-size: 26rpx;
  color: #333;
  word-break: break-all;
}
</style>
