<template>
    <view class="claim-container">
        <!-- 加载状态 -->
        <view class="loading-container" v-if="isLoading">
            <view class="loading-icon">⏳</view>
            <view class="loading-text">加载中...</view>
        </view>

        <!-- 中奖信息 -->
        <view class="claim-content" v-if="!isLoading && recordDetail">
            <!-- 奖品信息 -->
            <view class="prize-section">
                <view class="prize-icon">🎁</view>
                <view class="prize-title">恭喜您获得</view>
                <view class="prize-name">{{ recordDetail.prizeName }}</view>
                <view class="prize-desc" v-if="recordDetail.prizeValue">{{ recordDetail.prizeValue }}</view>
            </view>

            <!-- 中奖记录信息 -->
            <view class="record-info">
                <view class="info-item">
                    <text class="label">中奖时间：</text>
                    <text class="value">{{ formatTime(recordDetail.drawTime) }}</text>
                </view>
                <view class="info-item">
                    <text class="label">记录编号：</text>
                    <text class="value">{{ recordDetail.recordId }}</text>
                </view>
                <view class="info-item">
                    <text class="label">领取状态：</text>
                    <text class="value status" :class="{ 'claimed': recordDetail.claimStatus === '1' }">
                        {{ recordDetail.claimStatus === '1' ? '已领取' : '待领取' }}
                    </text>
                </view>
            </view>

            <!-- 领取说明 -->
            <view class="claim-instruction" v-if="claimInstruction">
                <view class="instruction-title">领取说明</view>
                <view class="instruction-content">{{ claimInstruction }}</view>
            </view>

            <!-- 微信二维码 -->
            <view class="wechat-qrcode" v-if="activityInfo && activityInfo.wechatQrcode">
                <image :src="getFullImageUrl(activityInfo.wechatQrcode)" class="qrcode-img" mode="aspectFit"
                    @error="handleImageError" @load="handleImageLoad"></image>
                <view class="qrcode-error" v-if="imageLoadError">
                    <text>二维码加载失败</text>
                </view>
            </view>

            <!-- 操作按钮 -->
            <view class="action-buttons">
                <view class="btn btn-secondary" @click="goBack">
                    <text>返回</text>
                </view>
                <view class="btn btn-primary" @click="markAsClaimed" v-if="recordDetail.claimStatus === '0'">
                    <text>确认领取</text>
                </view>
            </view>
        </view>

        <!-- 错误状态 -->
        <view class="error-container" v-if="!isLoading && !recordDetail">
            <view class="error-icon">❌</view>
            <view class="error-text">记录不存在或已过期</view>
            <view class="btn btn-primary" @click="goBack">
                <text>返回</text>
            </view>
        </view>
    </view>
</template>

<script>
import { lotteryApi, configApi, getImageUrl } from '@/utils/api.js'

export default {
    data() {
        return {
            recordId: '',
            recordDetail: null,
            activityInfo: null,
            claimInstruction: '',
            isLoading: true,
            merchantCode: '',
            merchantConfig: {},
            imageLoadError: false
        }
    },

    computed: {
        // 解析UI配置
        uiConfig() {
            const uiConfigStr = this.merchantConfig.ui_config
            if (uiConfigStr) {
                try {
                    return JSON.parse(uiConfigStr)
                } catch (e) {
                    console.error('UI配置解析失败:', e)
                    return {}
                }
            }
            return {}
        },

        // 主题色彩
        primaryColor() {
            return this.uiConfig.primaryColor || '#667eea'
        }
    },

    watch: {
        // 监听主题色彩变化，动态设置导航栏颜色
        primaryColor: {
            handler(newColor) {
                if (newColor) {
                    uni.setNavigationBarColor({
                        frontColor: '#ffffff',
                        backgroundColor: newColor
                    })
                }
            },
            immediate: true
        }
    },

    onLoad(options) {
        this.recordId = options.recordId || ''
        this.merchantCode = options.merchantCode || '002'
        this.initPage()
    },

    methods: {
        async initPage() {
            try {
                // 加载商家信息
                await this.loadMerchantInfo()

                if (this.recordId) {
                    await this.loadRecordDetail()
                } else {
                    this.isLoading = false
                }
            } catch (error) {
                console.error('页面初始化失败:', error)
                this.isLoading = false
            }
        },

        async loadMerchantInfo() {
            try {
                const res = await configApi.getMerchantConfig(this.merchantCode)
                if (res.code === 200) {
                    this.merchantConfig = res.data || {}
                }
            } catch (error) {
                console.error('获取商家配置失败:', error)
                this.merchantConfig = {}
            }
        },
        async loadRecordDetail() {
            try {
                const res = await lotteryApi.getRecordDetail(this.recordId)
                if (res.code === 200) {
                    this.recordDetail = res.data

                    // 加载活动信息
                    if (this.recordDetail.activityId) {
                        await this.loadActivityInfo()
                    }
                }
            } catch (error) {
                console.error('获取记录详情失败:', error)
                uni.showToast({
                    title: '获取详情失败',
                    icon: 'none'
                })
            } finally {
                this.isLoading = false
            }
        },

        async loadActivityInfo() {
            try {
                const res = await lotteryApi.getLotteryActivity(this.recordDetail.activityId)
                if (res.code === 200 && res.data) {
                    this.activityInfo = res.data
                    this.claimInstruction = res.data.claimInstruction || '请到前台出示此页面领取奖品'
                }
            } catch (error) {
                console.error('获取活动信息失败:', error)
                this.claimInstruction = '请到前台出示此页面领取奖品'
            }
        },

        async markAsClaimed() {
            try {
                const res = await lotteryApi.markAsClaimed(this.recordId)
                if (res.code === 200) {
                    this.recordDetail.claimStatus = '1'
                    this.recordDetail.claimTime = new Date().toISOString()
                    uni.showToast({
                        title: '领取成功',
                        icon: 'success'
                    })
                } else {
                    uni.showToast({
                        title: res.msg || '操作失败',
                        icon: 'none'
                    })
                }
            } catch (error) {
                console.error('标记领取失败:', error)
                uni.showToast({
                    title: '操作失败',
                    icon: 'none'
                })
            }
        },

        goBack() {
            const pages = getCurrentPages()
            if (pages.length > 1) {
                uni.navigateBack()
            } else {
                uni.redirectTo({
                    url: '/pages/lottery/lottery'
                })
            }
        },

        formatTime(timeStr) {
            if (!timeStr) return ''
            const date = new Date(timeStr)
            return `${date.getFullYear()}-${(date.getMonth() + 1).toString().padStart(2, '0')}-${date.getDate().toString().padStart(2, '0')} ${date.getHours().toString().padStart(2, '0')}:${date.getMinutes().toString().padStart(2, '0')}`
        },

        // 获取完整的图片URL
        getFullImageUrl(imagePath) {
            const fullUrl = getImageUrl(imagePath)
            console.log('Claim页面 - 原始图片路径:', imagePath)
            console.log('Claim页面 - 处理后的图片URL:', fullUrl)
            return fullUrl
        },

        // 图片加载错误处理
        handleImageError(event) {
            console.error('Claim页面 - 微信二维码图片加载失败:', event)
            console.error('Claim页面 - 图片URL:', this.getFullImageUrl(this.activityInfo.wechatQrcode))
            this.imageLoadError = true
        },

        // 图片加载成功处理
        handleImageLoad(event) {
            console.log('Claim页面 - 微信二维码图片加载成功:', event)
            this.imageLoadError = false
        }
    }
}
</script>

<style lang="scss" scoped>
.claim-container {
    min-height: 100vh;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    padding: 40rpx;
}

.loading-container,
.error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 60vh;
    text-align: center;
}

.loading-icon,
.error-icon {
    font-size: 80rpx;
    margin-bottom: 30rpx;
}

.loading-text,
.error-text {
    font-size: 32rpx;
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 40rpx;
}

.claim-content {
    background: rgba(255, 255, 255, 0.95);
    border-radius: 20rpx;
    padding: 40rpx;
    margin-bottom: 40rpx;
}

.prize-section {
    text-align: center;
    margin-bottom: 40rpx;
    padding-bottom: 40rpx;
    border-bottom: 1px solid #eee;
}

.prize-icon {
    font-size: 80rpx;
    margin-bottom: 20rpx;
}

.prize-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 10rpx;
}

.prize-name {
    font-size: 36rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 10rpx;
}

.prize-desc {
    font-size: 28rpx;
    color: #999;
}

.record-info {
    margin-bottom: 40rpx;
}

.info-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 20rpx 0;
    border-bottom: 1px solid #f5f5f5;
}

.label {
    font-size: 28rpx;
    color: #666;
}

.value {
    font-size: 28rpx;
    color: #333;
}

.status.claimed {
    color: #52c41a;
}

.claim-instruction {
    margin-bottom: 40rpx;
    padding: 30rpx;
    background: #f8f9fa;
    border-radius: 10rpx;
}

.instruction-title {
    font-size: 30rpx;
    font-weight: bold;
    color: #333;
    margin-bottom: 20rpx;
}

.instruction-content {
    font-size: 28rpx;
    color: #666;
    line-height: 1.6;
}

.wechat-qrcode {
    text-align: center;
    margin-bottom: 40rpx;
}

.qrcode-title {
    font-size: 28rpx;
    color: #666;
    margin-bottom: 20rpx;
}

.qrcode-img {
    width: 200rpx;
    height: 200rpx;
    border-radius: 10rpx;
    margin-bottom: 15rpx;
}

.qrcode-desc {
    color: #666;
    font-size: 24rpx;
    line-height: 1.4;
}

.qrcode-error {
    color: #ff4757;
    font-size: 24rpx;
    padding: 20rpx;
    background: rgba(255, 71, 87, 0.1);
    border-radius: 8rpx;
    margin-top: 15rpx;
}

.action-buttons {
    display: flex;
    gap: 20rpx;
}

.btn {
    flex: 1;
    height: 80rpx;
    border-radius: 40rpx;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30rpx;
    font-weight: bold;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea, #764ba2);
    color: white;
}

.btn-secondary {
    background: #f5f5f5;
    color: #666;
}
</style>