# 中奖页面功能说明

## 功能概述

当用户在抽奖活动中中奖后，系统会显示一个精美的中奖弹窗，展示以下内容：
- 中奖祝贺信息和动画效果
- 奖品名称和描述
- 中奖时间
- 领取说明
- 微信二维码（用于关注商家微信）
- 操作按钮（稍后领取/立即领取）

## 组件结构

### WinningModal 组件

位置：`/components/WinningModal/WinningModal.vue`

#### Props 参数

- `visible` (Boolean): 控制弹窗显示/隐藏
- `result` (Object): 中奖结果数据
- `activityInfo` (Object): 活动信息（包含微信二维码和领取说明）
- `autoClose` (Boolean): 是否自动关闭，默认 true
- `autoCloseDelay` (Number): 自动关闭延迟时间（毫秒），默认 5000

#### Events 事件

- `@close`: 弹窗关闭事件
- `@claim`: 点击立即领取事件

## 数据结构

### result 对象结构
```javascript
{
  isWinner: '1',           // 是否中奖：'1'-中奖，'0'-未中奖
  prizeName: '奖品名称',    // 奖品名称
  prizeDesc: '奖品描述',    // 奖品描述（可选）
  drawTime: '2024-01-01T12:00:00Z',  // 中奖时间
  recordId: 'record_123',   // 记录ID
  claimStatus: '0'         // 领取状态：'0'-待领取，'1'-已领取
}
```

### activityInfo 对象结构
```javascript
{
  activityName: '活动名称',
  claimInstruction: '领取说明文本',  // 详细的领取说明
  wechatQrcode: 'https://...'      // 微信二维码图片URL
}
```

## 使用方法

### 1. 在抽奖页面中使用

```vue
<template>
  <WinningModal 
    :visible="showWinningModal" 
    :result="currentWinningResult" 
    :activityInfo="currentActivity"
    :autoClose="true" 
    :autoCloseDelay="5000"
    @close="handleWinningModalClose" 
    @claim="handleClaimPrize" 
  />
</template>

<script>
import WinningModal from '@/components/WinningModal/WinningModal.vue'

export default {
  components: {
    WinningModal
  },
  data() {
    return {
      showWinningModal: false,
      currentWinningResult: null,
      currentActivity: null
    }
  },
  methods: {
    // 显示中奖弹窗
    showWinning(result, activity) {
      this.currentWinningResult = result
      this.currentActivity = activity
      this.showWinningModal = true
    },
    
    // 处理弹窗关闭
    handleWinningModalClose() {
      this.showWinningModal = false
      this.currentWinningResult = null
    },
    
    // 处理立即领取
    handleClaimPrize(result) {
      uni.navigateTo({
        url: `/pages/claim/claim?recordId=${result.recordId}`
      })
    }
  }
}
</script>
```

### 2. 测试功能

访问测试页面：`/pages/test-modal/test-modal`

该页面提供了测试按钮，可以预览中奖弹窗的效果。

## 样式特性

- 渐变背景色彩
- 烟花动画效果
- 弹跳动画图标
- 响应式布局
- 半透明遮罩层
- 圆角卡片设计

## 注意事项

1. 微信二维码图片需要确保URL可访问
2. 领取说明文本支持换行显示
3. 弹窗会在指定时间后自动关闭
4. 点击遮罩层也可以关闭弹窗
5. 只有中奖用户才会显示"立即领取"按钮

## 配置说明

活动信息中的微信二维码和领取说明可以通过后台管理系统进行配置：

- `wechatQrcode`: 微信二维码图片URL
- `claimInstruction`: 领取说明文本，支持详细的领取流程描述

如果没有配置微信二维码，该部分将不会显示。
如果没有配置领取说明，将使用默认文本。
