<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.LotteryActivityMapper">
    
    <resultMap type="LotteryActivity" id="LotteryActivityResult">
        <id     property="activityId"     column="activity_id"     />
        <result property="merchantId"     column="merchant_id"     />
        <result property="activityName"   column="activity_name"   />
        <result property="activityDesc"   column="activity_desc"   />
        <result property="startTime"      column="start_time"      />
        <result property="endTime"        column="end_time"        />
        <result property="prizeConfig"    column="prize_config"    />
        <result property="drawRules"      column="draw_rules"      />
        <result property="dailyLimit"     column="daily_limit"     />
        <result property="totalLimit"     column="total_limit"     />
        <result property="status"         column="status"          />
        <result property="delFlag"        column="del_flag"        />
        <result property="createBy"       column="create_by"       />
        <result property="createTime"     column="create_time"     />
        <result property="updateBy"       column="update_by"       />
        <result property="updateTime"     column="update_time"     />
        <result property="remark"         column="remark"          />
        <result property="merchantName"   column="merchant_name"   />
        <result property="drawnCount"     column="drawn_count"     />
        <result property="winCount"       column="win_count"       />
        <result property="claimInstruction" column="claim_instruction" />
        <result property="wechatQrcode"   column="wechat_qrcode"   />
    </resultMap>
    
    <sql id="selectLotteryActivityVo">
        select a.activity_id, a.merchant_id, a.activity_name, a.activity_desc, a.start_time,
               a.end_time, a.prize_config, a.draw_rules, a.daily_limit, a.total_limit,
               a.status, a.claim_instruction, a.wechat_qrcode, a.del_flag, a.create_by, a.create_time, a.update_by, a.update_time,
               a.remark, m.merchant_name
        from lottery_activity a
        left join merchant m on a.merchant_id = m.merchant_id
    </sql>
    
    <select id="selectLotteryActivityList" parameterType="LotteryActivity" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        <where>
            <if test="merchantId != null">
                AND a.merchant_id = #{merchantId}
            </if>
            <if test="activityName != null and activityName != ''">
                AND a.activity_name like concat('%', #{activityName}, '%')
            </if>
            <if test="status != null and status != ''">
                AND a.status = #{status}
            </if>
            <if test="merchantName != null and merchantName != ''">
                AND m.merchant_name like concat('%', #{merchantName}, '%')
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(a.start_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(a.end_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            AND a.del_flag = '0'
            ${params.merchantDataScopeFilter}
        </where>
        order by a.activity_id desc
    </select>
    
    <select id="selectLotteryActivityById" parameterType="Long" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        where a.activity_id = #{activityId} and a.del_flag = '0'
    </select>
    
    <select id="selectLotteryActivitiesByMerchantId" parameterType="Long" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        where a.merchant_id = #{merchantId} and a.del_flag = '0'
        order by a.start_time desc
    </select>
    
    <select id="selectValidLotteryActivities" parameterType="Long" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        where a.merchant_id = #{merchantId} and a.status = '0' and a.del_flag = '0'
        and a.start_time &lt;= now() and a.end_time &gt;= now()
        order by a.start_time desc
    </select>
    
    <select id="selectCurrentValidActivity" parameterType="Long" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        where a.merchant_id = #{merchantId} and a.status = '0' and a.del_flag = '0'
        and a.start_time &lt;= now() and a.end_time &gt;= now()
        order by a.start_time desc
        limit 1
    </select>
    
    <select id="selectEndingSoonActivities" parameterType="int" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        where a.status = '0' and a.del_flag = '0'
        and a.end_time between now() and date_add(now(), interval #{hours} hour)
        order by a.end_time asc
    </select>
    
    <select id="selectExpiredActivities" resultMap="LotteryActivityResult">
        <include refid="selectLotteryActivityVo"/>
        where a.status = '0' and a.del_flag = '0'
        and a.end_time &lt; now()
        order by a.end_time asc
    </select>
    
    <select id="countActivitiesByMerchantId" parameterType="Long" resultType="int">
        select count(*) from lottery_activity 
        where merchant_id = #{merchantId} and del_flag = '0'
    </select>
    
    <insert id="insertLotteryActivity" parameterType="LotteryActivity" useGeneratedKeys="true" keyProperty="activityId">
        insert into lottery_activity
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">merchant_id,</if>
            <if test="activityName != null and activityName != ''">activity_name,</if>
            <if test="activityDesc != null">activity_desc,</if>
            <if test="startTime != null">start_time,</if>
            <if test="endTime != null">end_time,</if>
            <if test="prizeConfig != null">prize_config,</if>
            <if test="drawRules != null">draw_rules,</if>
            <if test="dailyLimit != null">daily_limit,</if>
            <if test="totalLimit != null">total_limit,</if>
            <if test="status != null">status,</if>
            <if test="claimInstruction != null">claim_instruction,</if>
            <if test="wechatQrcode != null">wechat_qrcode,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantId != null">#{merchantId},</if>
            <if test="activityName != null and activityName != ''">#{activityName},</if>
            <if test="activityDesc != null">#{activityDesc},</if>
            <if test="startTime != null">#{startTime},</if>
            <if test="endTime != null">#{endTime},</if>
            <if test="prizeConfig != null">#{prizeConfig},</if>
            <if test="drawRules != null">#{drawRules},</if>
            <if test="dailyLimit != null">#{dailyLimit},</if>
            <if test="totalLimit != null">#{totalLimit},</if>
            <if test="status != null">#{status},</if>
            <if test="claimInstruction != null">#{claimInstruction},</if>
            <if test="wechatQrcode != null">#{wechatQrcode},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>
    
    <update id="updateLotteryActivity" parameterType="LotteryActivity">
        update lottery_activity
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantId != null">merchant_id = #{merchantId},</if>
            <if test="activityName != null and activityName != ''">activity_name = #{activityName},</if>
            <if test="activityDesc != null">activity_desc = #{activityDesc},</if>
            <if test="startTime != null">start_time = #{startTime},</if>
            <if test="endTime != null">end_time = #{endTime},</if>
            <if test="prizeConfig != null">prize_config = #{prizeConfig},</if>
            <if test="drawRules != null">draw_rules = #{drawRules},</if>
            <if test="dailyLimit != null">daily_limit = #{dailyLimit},</if>
            <if test="totalLimit != null">total_limit = #{totalLimit},</if>
            <if test="status != null">status = #{status},</if>
            <if test="claimInstruction != null">claim_instruction = #{claimInstruction},</if>
            <if test="wechatQrcode != null">wechat_qrcode = #{wechatQrcode},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where activity_id = #{activityId}
    </update>
    
    <delete id="deleteLotteryActivityById" parameterType="Long">
        update lottery_activity set del_flag = '2' where activity_id = #{activityId}
    </delete>
    
    <delete id="deleteLotteryActivityByIds" parameterType="String">
        update lottery_activity set del_flag = '2' where activity_id in 
        <foreach item="activityId" collection="array" open="(" separator="," close=")">
            #{activityId}
        </foreach>
    </delete>
    
    <delete id="deleteLotteryActivityByMerchantId" parameterType="Long">
        update lottery_activity set del_flag = '2' where merchant_id = #{merchantId}
    </delete>
    
    <update id="updateExpiredActivitiesStatus">
        update lottery_activity set status = '1', update_time = sysdate()
        where status = '0' and del_flag = '0' and end_time &lt; now()
    </update>

</mapper>
