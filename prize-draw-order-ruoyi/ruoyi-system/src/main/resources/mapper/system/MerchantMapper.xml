<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ruoyi.system.mapper.MerchantMapper">
    
    <resultMap type="Merchant" id="MerchantResult">
        <id     property="merchantId"      column="merchant_id"      />
        <result property="merchantName"    column="merchant_name"    />
        <result property="merchantCode"    column="merchant_code"    />
        <result property="contactPerson"   column="contact_person"   />
        <result property="contactPhone"    column="contact_phone"    />
        <result property="address"         column="address"          />
        <result property="expireTime"      column="expire_time"      />
        <result property="status"          column="status"           />
        <result property="delFlag"         column="del_flag"         />
        <result property="createBy"        column="create_by"        />
        <result property="createTime"      column="create_time"      />
        <result property="updateBy"        column="update_by"        />
        <result property="updateTime"      column="update_time"      />
        <result property="remark"          column="remark"           />
    </resultMap>
    
    <sql id="selectMerchantVo">
        select merchant_id, merchant_name, merchant_code, contact_person, contact_phone,
               address, expire_time, status, del_flag, create_by,
               create_time, update_by, update_time, remark
        from merchant
    </sql>
    
    <select id="selectMerchantList" parameterType="Merchant" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        <where>
            <if test="merchantName != null and merchantName != ''">
                AND merchant_name like concat('%', #{merchantName}, '%')
            </if>
            <if test="merchantCode != null and merchantCode != ''">
                AND merchant_code like concat('%', #{merchantCode}, '%')
            </if>
            <if test="contactPerson != null and contactPerson != ''">
                AND contact_person like concat('%', #{contactPerson}, '%')
            </if>
            <if test="contactPhone != null and contactPhone != ''">
                AND contact_phone like concat('%', #{contactPhone}, '%')
            </if>
            <if test="status != null and status != ''">
                AND status = #{status}
            </if>
            <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
                AND date_format(create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
            </if>
            <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
                AND date_format(create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
            </if>
            AND del_flag = '0'
            ${params.merchantDataScopeFilter}
        </where>
        order by merchant_id desc
    </select>
    
    <select id="selectMerchantById" parameterType="Long" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        where merchant_id = #{merchantId} and del_flag = '0'
    </select>
    
    <select id="selectMerchantByCode" parameterType="String" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        where merchant_code = #{merchantCode} and del_flag = '0'
    </select>
    
    <select id="checkMerchantCodeUnique" parameterType="String" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        where merchant_code = #{merchantCode} and del_flag = '0' limit 1
    </select>
    
    <select id="selectExpiringSoonMerchants" parameterType="int" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        where status = '0' and del_flag = '0' 
        and expire_time is not null 
        and expire_time between now() and date_add(now(), interval #{days} day)
        order by expire_time asc
    </select>
    
    <select id="selectExpiredMerchants" resultMap="MerchantResult">
        <include refid="selectMerchantVo"/>
        where status != '2' and del_flag = '0' 
        and expire_time is not null 
        and expire_time &lt; now()
        order by expire_time asc
    </select>
    
    <insert id="insertMerchant" parameterType="Merchant" useGeneratedKeys="true" keyProperty="merchantId">
        insert into merchant
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="merchantName != null and merchantName != ''">merchant_name,</if>
            <if test="merchantCode != null and merchantCode != ''">merchant_code,</if>
            <if test="contactPerson != null">contact_person,</if>
            <if test="contactPhone != null">contact_phone,</if>
            <if test="address != null">address,</if>
            <if test="expireTime != null">expire_time,</if>
            <if test="status != null">status,</if>
            <if test="delFlag != null">del_flag,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="merchantName != null and merchantName != ''">#{merchantName},</if>
            <if test="merchantCode != null and merchantCode != ''">#{merchantCode},</if>
            <if test="contactPerson != null">#{contactPerson},</if>
            <if test="contactPhone != null">#{contactPhone},</if>
            <if test="address != null">#{address},</if>
            <if test="expireTime != null">#{expireTime},</if>
            <if test="status != null">#{status},</if>
            <if test="delFlag != null">#{delFlag},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
        </trim>
    </insert>
    
    <update id="updateMerchant" parameterType="Merchant">
        update merchant
        <trim prefix="SET" suffixOverrides=",">
            <if test="merchantName != null and merchantName != ''">merchant_name = #{merchantName},</if>
            <if test="merchantCode != null and merchantCode != ''">merchant_code = #{merchantCode},</if>
            <if test="contactPerson != null">contact_person = #{contactPerson},</if>
            <if test="contactPhone != null">contact_phone = #{contactPhone},</if>
            <if test="address != null">address = #{address},</if>
            <if test="expireTime != null">expire_time = #{expireTime},</if>
            <if test="status != null">status = #{status},</if>
            <if test="delFlag != null">del_flag = #{delFlag},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where merchant_id = #{merchantId}
    </update>
    
    <delete id="deleteMerchantById" parameterType="Long">
        update merchant set del_flag = '2' where merchant_id = #{merchantId}
    </delete>
    
    <delete id="deleteMerchantByIds" parameterType="String">
        update merchant set del_flag = '2' where merchant_id in 
        <foreach item="merchantId" collection="array" open="(" separator="," close=")">
            #{merchantId}
        </foreach>
    </delete>
    
    <update id="updateExpiredMerchantsStatus">
        update merchant set status = '2', update_time = sysdate()
        where status != '2' and del_flag = '0' 
        and expire_time is not null 
        and expire_time &lt; now()
    </update>
    
</mapper>
