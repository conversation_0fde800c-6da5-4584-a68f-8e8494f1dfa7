package com.ruoyi.system.domain;

import java.util.Date;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.ruoyi.common.annotation.Excel;
import com.ruoyi.common.annotation.Excel.ColumnType;
import com.ruoyi.common.core.domain.BaseEntity;

/**
 * 抽奖活动表 lottery_activity
 * 
 * <AUTHOR>
 */
public class LotteryActivity extends BaseEntity
{
    private static final long serialVersionUID = 1L;

    /** 活动ID */
    @Excel(name = "活动ID", cellType = ColumnType.NUMERIC)
    private Long activityId;

    /** 商家ID */
    @Excel(name = "商家ID", cellType = ColumnType.NUMERIC)
    private Long merchantId;

    /** 活动名称 */
    @Excel(name = "活动名称")
    private String activityName;

    /** 活动描述 */
    @Excel(name = "活动描述")
    private String activityDesc;

    /** 开始时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "开始时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    /** 结束时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "结束时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    /** 奖品配置（JSON格式） */
    private String prizeConfig;

    /** 抽奖规则 */
    private String drawRules;

    /** 每日抽奖次数限制 */
    @Excel(name = "每日限制次数")
    private Integer dailyLimit;

    /** 总抽奖次数限制（0为不限制） */
    @Excel(name = "总限制次数")
    private Integer totalLimit;

    /** 状态（0正常 1停用） */
    @Excel(name = "状态", readConverterExp = "0=正常,1=停用")
    private String status;

    /** 删除标志（0代表存在 2代表删除） */
    private String delFlag;

    /** 商家名称（关联查询用） */
    @Excel(name = "商家名称")
    private String merchantName;

    /** 已抽奖次数（统计用） */
    @Excel(name = "已抽奖次数")
    private Integer drawnCount;

    /** 中奖次数（统计用） */
    @Excel(name = "中奖次数")
    private Integer winCount;

    /** 领取说明 */
    private String claimInstruction;

    /** 微信二维码图片路径 */
    private String wechatQrcode;

    public Long getActivityId()
    {
        return activityId;
    }

    public void setActivityId(Long activityId)
    {
        this.activityId = activityId;
    }

    @NotNull(message = "商家ID不能为空")
    public Long getMerchantId()
    {
        return merchantId;
    }

    public void setMerchantId(Long merchantId)
    {
        this.merchantId = merchantId;
    }

    @NotBlank(message = "活动名称不能为空")
    @Size(min = 0, max = 100, message = "活动名称长度不能超过100个字符")
    public String getActivityName()
    {
        return activityName;
    }

    public void setActivityName(String activityName)
    {
        this.activityName = activityName;
    }

    @Size(min = 0, max = 500, message = "活动描述长度不能超过500个字符")
    public String getActivityDesc()
    {
        return activityDesc;
    }

    public void setActivityDesc(String activityDesc)
    {
        this.activityDesc = activityDesc;
    }

    @NotNull(message = "开始时间不能为空")
    public Date getStartTime()
    {
        return startTime;
    }

    public void setStartTime(Date startTime)
    {
        this.startTime = startTime;
    }

    @NotNull(message = "结束时间不能为空")
    public Date getEndTime()
    {
        return endTime;
    }

    public void setEndTime(Date endTime)
    {
        this.endTime = endTime;
    }

    public String getPrizeConfig()
    {
        return prizeConfig;
    }

    public void setPrizeConfig(String prizeConfig)
    {
        this.prizeConfig = prizeConfig;
    }

    public String getDrawRules()
    {
        return drawRules;
    }

    public void setDrawRules(String drawRules)
    {
        this.drawRules = drawRules;
    }

    public Integer getDailyLimit()
    {
        return dailyLimit;
    }

    public void setDailyLimit(Integer dailyLimit)
    {
        this.dailyLimit = dailyLimit;
    }

    public Integer getTotalLimit()
    {
        return totalLimit;
    }

    public void setTotalLimit(Integer totalLimit)
    {
        this.totalLimit = totalLimit;
    }

    public String getStatus()
    {
        return status;
    }

    public void setStatus(String status)
    {
        this.status = status;
    }

    public String getDelFlag()
    {
        return delFlag;
    }

    public void setDelFlag(String delFlag)
    {
        this.delFlag = delFlag;
    }

    public String getMerchantName()
    {
        return merchantName;
    }

    public void setMerchantName(String merchantName)
    {
        this.merchantName = merchantName;
    }

    public Integer getDrawnCount()
    {
        return drawnCount;
    }

    public void setDrawnCount(Integer drawnCount)
    {
        this.drawnCount = drawnCount;
    }

    public Integer getWinCount()
    {
        return winCount;
    }

    public void setWinCount(Integer winCount)
    {
        this.winCount = winCount;
    }

    public String getClaimInstruction()
    {
        return claimInstruction;
    }

    public void setClaimInstruction(String claimInstruction)
    {
        this.claimInstruction = claimInstruction;
    }

    public String getWechatQrcode()
    {
        return wechatQrcode;
    }

    public void setWechatQrcode(String wechatQrcode)
    {
        this.wechatQrcode = wechatQrcode;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this,ToStringStyle.MULTI_LINE_STYLE)
            .append("activityId", getActivityId())
            .append("merchantId", getMerchantId())
            .append("activityName", getActivityName())
            .append("activityDesc", getActivityDesc())
            .append("startTime", getStartTime())
            .append("endTime", getEndTime())
            .append("prizeConfig", getPrizeConfig())
            .append("drawRules", getDrawRules())
            .append("dailyLimit", getDailyLimit())
            .append("totalLimit", getTotalLimit())
            .append("status", getStatus())
            .append("delFlag", getDelFlag())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .append("merchantName", getMerchantName())
            .append("drawnCount", getDrawnCount())
            .append("winCount", getWinCount())
            .append("claimInstruction", getClaimInstruction())
            .append("wechatQrcode", getWechatQrcode())
            .toString();
    }
}
