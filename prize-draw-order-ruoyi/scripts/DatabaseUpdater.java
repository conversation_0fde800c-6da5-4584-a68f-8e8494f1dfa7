import java.sql.Connection;
import java.sql.DriverManager;
import java.sql.Statement;
import java.sql.SQLException;
import java.sql.DatabaseMetaData;
import java.sql.ResultSet;

/**
 * 数据库更新工具
 * 用于执行数据库结构更新
 */
public class DatabaseUpdater {
    
    // 数据库连接信息
    private static final String DB_URL = "***************************************************************************************************************************************************************";
    private static final String DB_USER = "root";
    private static final String DB_PASSWORD = "9326ee82dc7a0aa6";
    
    public static void main(String[] args) {
        DatabaseUpdater updater = new DatabaseUpdater();
        updater.updateDatabase();
    }
    
    public void updateDatabase() {
        Connection conn = null;
        Statement stmt = null;
        
        try {
            // 加载MySQL驱动
            Class.forName("com.mysql.cj.jdbc.Driver");
            
            // 建立连接
            System.out.println("正在连接数据库...");
            conn = DriverManager.getConnection(DB_URL, DB_USER, DB_PASSWORD);
            System.out.println("数据库连接成功！");
            
            stmt = conn.createStatement();
            
            // 检查并添加 claim_instruction 字段
            if (!columnExists(conn, "lottery_activity", "claim_instruction")) {
                System.out.println("添加 claim_instruction 字段...");
                String sql1 = "ALTER TABLE `lottery_activity` ADD COLUMN `claim_instruction` text DEFAULT NULL COMMENT '领取说明' AFTER `status`";
                stmt.executeUpdate(sql1);
                System.out.println("claim_instruction 字段添加成功！");
            } else {
                System.out.println("claim_instruction 字段已存在，跳过添加。");
            }
            
            // 检查并添加 wechat_qrcode 字段
            if (!columnExists(conn, "lottery_activity", "wechat_qrcode")) {
                System.out.println("添加 wechat_qrcode 字段...");
                String sql2 = "ALTER TABLE `lottery_activity` ADD COLUMN `wechat_qrcode` varchar(500) DEFAULT NULL COMMENT '微信二维码图片路径' AFTER `claim_instruction`";
                stmt.executeUpdate(sql2);
                System.out.println("wechat_qrcode 字段添加成功！");
            } else {
                System.out.println("wechat_qrcode 字段已存在，跳过添加。");
            }
            
            // 尝试删除 merchant 表中的 wechat_qrcode 字段（如果存在）
            if (columnExists(conn, "merchant", "wechat_qrcode")) {
                System.out.println("删除 merchant 表中的 wechat_qrcode 字段...");
                String sql3 = "ALTER TABLE `merchant` DROP COLUMN `wechat_qrcode`";
                stmt.executeUpdate(sql3);
                System.out.println("merchant 表中的 wechat_qrcode 字段删除成功！");
            } else {
                System.out.println("merchant 表中的 wechat_qrcode 字段不存在，跳过删除。");
            }
            
            System.out.println("数据库更新完成！");
            
        } catch (ClassNotFoundException e) {
            System.err.println("MySQL驱动未找到: " + e.getMessage());
        } catch (SQLException e) {
            System.err.println("数据库操作失败: " + e.getMessage());
            e.printStackTrace();
        } finally {
            // 关闭连接
            try {
                if (stmt != null) stmt.close();
                if (conn != null) conn.close();
                System.out.println("数据库连接已关闭。");
            } catch (SQLException e) {
                System.err.println("关闭连接时出错: " + e.getMessage());
            }
        }
    }
    
    /**
     * 检查表中是否存在指定字段
     */
    private boolean columnExists(Connection conn, String tableName, String columnName) throws SQLException {
        DatabaseMetaData metaData = conn.getMetaData();
        ResultSet rs = metaData.getColumns(null, null, tableName, columnName);
        boolean exists = rs.next();
        rs.close();
        return exists;
    }
}
