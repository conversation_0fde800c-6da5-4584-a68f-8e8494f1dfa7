# 配置管理和抽奖活动字段修改总结

## 修改概述

本次修改主要包括以下内容：
1. 删除配置管理中的"抽奖配置"和"高级配置"标签页
2. 删除基本配置中的"微信二维码"字段
3. 在抽奖活动中增加"领取说明"字段和"微信二维码"字段

## 详细修改内容

### 1. 前端修改

#### 1.1 配置管理页面 (`ruoyi-ui/src/views/merchant/config/index.vue`)
- 删除"抽奖配置"标签页及相关代码
- 删除"高级配置"标签页及相关代码
- 删除基本配置中的微信二维码字段
- 删除相关的数据模型和方法

#### 1.2 抽奖活动表单页面 (`ruoyi-ui/src/views/lottery/activity/form.vue`)
- 添加"领取说明"字段（textarea）
- 添加"微信二维码"字段（image-upload组件）
- 更新表单数据模型

#### 1.3 抽奖活动列表页面 (`ruoyi-ui/src/views/lottery/activity/index.vue`)
- 更新表单重置方法，包含新字段

### 2. 后端修改

#### 2.1 实体类修改
**LotteryActivity.java**
- 添加 `claimInstruction` 字段（领取说明）
- 添加 `wechatQrcode` 字段（微信二维码）
- 添加对应的getter/setter方法
- 更新toString方法

**Merchant.java**
- 删除 `wechatQrcode` 字段
- 删除对应的getter/setter方法
- 更新toString方法

#### 2.2 Mapper XML修改
**LotteryActivityMapper.xml**
- 添加新字段的结果映射
- 更新SQL查询语句
- 更新insert和update语句

**MerchantMapper.xml**
- 删除微信二维码字段的映射
- 更新SQL查询语句
- 更新insert和update语句

#### 2.3 API接口修改
**ApiLotteryController.java**
- 添加 `getActivityDetail` 方法获取活动详情
- 添加 `getRecordDetail` 方法获取记录详情
- 添加 `markAsClaimed` 方法标记奖品已领取

### 3. 数据库修改

#### 3.1 表结构修改 (`sql/update_20241231.sql`)
```sql
-- 删除商家表中的微信二维码字段
ALTER TABLE `merchant` DROP COLUMN `wechat_qrcode`;

-- 在抽奖活动表中添加领取说明字段
ALTER TABLE `lottery_activity` ADD COLUMN `claim_instruction` text DEFAULT NULL COMMENT '领取说明' AFTER `status`;

-- 在抽奖活动表中添加微信二维码字段
ALTER TABLE `lottery_activity` ADD COLUMN `wechat_qrcode` varchar(500) DEFAULT NULL COMMENT '微信二维码图片路径' AFTER `claim_instruction`;
```

#### 3.2 更新主SQL文件 (`sql/prize_draw_order.sql`)
- 删除merchant表中的wechat_qrcode字段
- 在lottery_activity表中添加claim_instruction和wechat_qrcode字段

### 4. 小程序端修改

#### 4.1 首页修改 (`prize-draw-order-uniapp/pages/index/index.vue`)
- 修改微信二维码显示逻辑，从活动信息中获取而不是从商家配置中获取
- 添加加载当前活动信息的方法

#### 4.2 结果页面修改 (`prize-draw-order-uniapp/pages/result/result.vue`)
- 修改领取说明获取逻辑，从活动信息中获取而不是从配置中获取

#### 4.3 领取页面创建 (`prize-draw-order-uniapp/pages/claim/claim.vue`)
- 创建完整的领取页面
- 显示奖品信息、中奖记录、领取说明和微信二维码
- 支持标记奖品为已领取

#### 4.4 API接口更新 (`prize-draw-order-uniapp/utils/api.js`)
- 添加 `getLotteryActivity` 方法获取单个活动信息
- 添加 `getRecordDetail` 方法获取记录详情
- 添加 `markAsClaimed` 方法标记已领取

## 功能影响

### 正面影响
1. **简化配置管理**：删除了不常用的抽奖配置和高级配置，使界面更简洁
2. **增强活动管理**：每个活动可以有独立的领取说明和微信二维码，更灵活
3. **改善用户体验**：用户在领取页面可以看到具体的领取说明和相关二维码

### 注意事项
1. **数据迁移**：如果现有系统中商家表有微信二维码数据，需要手动迁移到相应的活动中
2. **兼容性**：小程序端的代码已更新为从活动中获取信息，确保向后兼容
3. **权限检查**：新增的API接口已添加适当的权限控制

## 测试建议

1. **配置管理测试**：验证删除的标签页不再显示，基本配置功能正常
2. **活动管理测试**：验证新字段的添加、编辑、显示功能
3. **小程序测试**：验证首页、抽奖页面、领取页面的功能正常
4. **数据库测试**：执行迁移脚本，验证表结构修改正确
5. **API测试**：测试新增的API接口功能正常

## 部署步骤

1. 执行数据库迁移脚本 `sql/update_20241231.sql`
2. 部署后端代码
3. 部署前端管理系统代码
4. 部署小程序代码
5. 验证所有功能正常工作
